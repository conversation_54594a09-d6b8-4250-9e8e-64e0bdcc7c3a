{% extends "base.html" %}

{% block title %}气密性泄漏量对比 - NVH数据管理系统{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/airtightness.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">气密性泄漏量对比</h1>
</div>

<!-- 车型选择区域 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>车型选择</h5>
    </div>
    <div class="card-body">
        <div class="row g-3 align-items-end">
            <div class="col-md-8">
                <div class="vehicle-multiselect" id="vehicle-multiselect">
                    <div class="multiselect-container">
                        <div class="multiselect-input-container">
                            <input type="text" class="form-control multiselect-input" placeholder="点击选择车型..." readonly>
                            <i class="fas fa-chevron-down multiselect-arrow"></i>
                        </div>
                        <div class="multiselect-dropdown">
                            <div class="multiselect-search">
                                <input type="text" class="form-control form-control-sm" placeholder="搜索车型...">
                            </div>
                            <div class="multiselect-options">
                                <!-- 动态加载选项 -->
                            </div>
                        </div>
                    </div>
                    <div class="selected-items mt-2">
                        <!-- 已选择的车型标签 -->
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <button type="button" class="btn btn-primary" id="generate-comparison-btn" disabled>
                    <i class="fas fa-chart-bar me-1"></i>生成对比表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>泄漏量对比结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="vehicle-count" class="badge bg-secondary">0 个车型</span>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                <i class="fas fa-download me-1"></i>导出数据
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="comparison-table">
                <thead class="table-dark">
                    <!-- 动态生成表头 -->
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 测试信息 -->
        <div class="mt-4">
            <h6>测试信息</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered" id="test-info-table">
                    <thead class="table-light">
                        <tr>
                            <th>车型</th>
                            <th>测试日期</th>
                            <th>测试工程师</th>
                            <th>测试地点</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态生成测试信息 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择车型生成对比表</h5>
        <p class="text-muted">选择一个或多个车型，点击"生成对比表"按钮查看泄漏量对比数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在生成对比数据...</p>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/airtightness.js') }}"></script>
{% endblock %}
